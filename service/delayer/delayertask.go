package delayer

import (
	"assistantdeskgo/components"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type DelayerTaskMessage struct {
	AppKey       string `json:"appKey"  mapstructure:"appKey"`
	CallbackTime int64  `json:"callbackTime"  mapstructure:"callbackTime"`
	ContentType  string `json:"contentType"   mapstructure:"contentType"`
	Content      string `json:"content"    mapstructure:"content"`
}

func (d DelayerTaskMessage) Consumer(ctx *gin.Context) error {
	var client DelayerTaskConsumer
	switch d.ContentType {
	case components.DelayerContentTypeForTouchCallTime:
		client = &TouchCallTimeData{}
	case components.DelayerContentTypeForNoticeCallTime:
		client = &NoticeCallTimeData{}
	default:
		zlog.Infof(ctx, "[DelayerTaskConsumer] contentType undefined, contentType: %s", d.ContentType)
		return nil
	}

	return client.Consumer(ctx, d)
}

type DelayerTaskConsumer interface {
	Consumer(ctx *gin.Context, input DelayerTaskMessage) (err error)
}
