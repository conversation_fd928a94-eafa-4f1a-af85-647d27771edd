package defines

var ClassDict = map[int8]string{
	1: "产品功能",
	2: "内部培训",
	3: "规则同步",
	4: "标杆上墙",
	5: "违纪通晒",
	6: "企业动态",
	7: "其他",
}

var StatusDict = map[int8]string{
	1: "编辑中",
	2: "已发布",
	3: "已结束",
	4: "发布中",
}

var ScopeDict = map[int8]string{
	1: "筛选员工",
	2: "全体员工",
}

var RandomUserNameList = []string{
	"明", "辉", "杰", "涛", "鹏", "强", "磊", "军", "勇", "伟",
	"刚", "毅", "光", "亮", "星", "晨", "峰", "松", "林", "森",
	"宁", "安", "康", "平", "顺", "和", "福", "祥", "瑞", "兴",
	"山", "河", "海", "风", "云", "雨", "雪", "冰", "火", "雷",
	"文", "武", "德", "才", "智", "慧", "仁", "义", "礼", "信",
	"春", "夏", "秋", "冬", "日", "月", "星", "辰", "天", "地",
	"金", "木", "水", "火", "土", "石", "玉", "珠", "宝", "珍",
	"芳", "敏", "静", "丽", "霞", "燕", "婷", "娟", "娜", "玲",
	"红", "梅", "兰", "竹", "菊", "莲", "桃", "李", "桂", "樱",
}

var MaxReadUserDisplay = 5

const (
	SystemNoticeStatusEdit     = 1 // 编辑中
	SystemNoticeStatusPublish  = 2 // 已发布
	SystemNoticeStatusFinish   = 3 // 已结束
	SystemNoticeStatusSchedule = 4 // 发布中

	NoticeScheduleTypePub = 1 // 定时发布
	NoticeScheduleTypeEnd = 2 // 定时结束

	SystemNoticeScopeFilter = 1 // 筛选员工
	SystemNoticeScopeAll    = 2 // 全体员工

	NoticeContentType = 1 // 图文类型
	NoticeVideoType   = 2 // 视频类型

	NoticeFeedbackLikeType   = 1 // 点赞
	NoticeFeedbackUnLikeType = 2 // 点踩

	NoticeReadType   = 1 // 已读通知
	NoticeUnReadType = 2 // 未读通知
)

const (
	TimeRangeUnlimited       = 0 // 不限评论时间
	TimeRangeLast7Days       = 1 // 最近7天
	TimeRangeToday           = 2 // 今天
	TimeRangeYesterday       = 3 // 昨天
	TimeRangeBeforeYesterday = 4 // 前天

	CommentTypeAll     = 0 // 全部评论
	CommentTypeSeleted = 1 // 精选评论

	CommentSortByTimeDesc = 0 // 按时间降序
	CommentSortByLikeDesc = 1 // 按点赞数降序
)
