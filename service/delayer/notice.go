package delayer

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models/notice"
	"assistantdeskgo/utils"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

type NoticeCallTimeData struct {
	NoticeId int64 `json:"noticeId"`
	Type     int8  `json:"type"`
}

func (o *NoticeCallTimeData) Consumer(ctx *gin.Context, input DelayerTaskMessage) (err error) {
	data := &NoticeCallTimeData{}
	err = jsoniter.UnmarshalFromString(input.Content, data)
	if err != nil {
		zlog.Warnf(ctx, "notice delayer msg consumer failed, input:%+v, err: %+v", input, err)
		return err
	}

	zlog.Infof(ctx, "[NoticeCallTimeData.Consumer] processing notice schedule task, noticeId: %d, type: %d", data.NoticeId, data.Type)

	if data.Type == defines.NoticeScheduleTypePub {
		// 定时发布逻辑
		err = publishScheduledNotice(ctx, data.NoticeId)
		if err != nil {
			zlog.Errorf(ctx, "[NoticeCallTimeData.Consumer] publish scheduled notice failed, noticeId: %d, err: %+v", data.NoticeId, err)
			return err
		}
		zlog.Infof(ctx, "[NoticeCallTimeData.Consumer] publish scheduled notice success, noticeId: %d", data.NoticeId)
	}

	if data.Type == defines.NoticeScheduleTypeEnd {
		// 定时结束逻辑
		err = finishScheduledNotice(ctx, data.NoticeId)
		if err != nil {
			zlog.Errorf(ctx, "[NoticeCallTimeData.Consumer] finish scheduled notice failed, noticeId: %d, err: %+v", data.NoticeId, err)
			return err
		}
		zlog.Infof(ctx, "[NoticeCallTimeData.Consumer] finish scheduled notice success, noticeId: %d", data.NoticeId)
	}

	return
}

// publishScheduledNotice 处理定时发布逻辑
func publishScheduledNotice(ctx *gin.Context, noticeId int64) error {
	// 开启事务确保数据一致性
	tx := helpers.MysqlClientFuDao.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			zlog.Errorf(ctx, "[publishScheduledNotice] panic occurred, noticeId: %d, panic: %+v", noticeId, r)
		}
	}()

	// 1. 获取通知信息
	noticeInfo, err := notice.SystemNoticeDao.GetById(ctx, noticeId, tx)
	if err != nil {
		tx.Rollback()
		zlog.Errorf(ctx, "[publishScheduledNotice] get notice failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	if noticeInfo == nil {
		tx.Rollback()
		zlog.Warnf(ctx, "[publishScheduledNotice] notice not found, noticeId: %d", noticeId)
		return nil // 通知不存在，可能已被删除，不返回错误
	}

	// 2. 检查当前状态是否为发布中状态
	if noticeInfo.Status != defines.SystemNoticeStatusSchedule {
		tx.Rollback()
		zlog.Warnf(ctx, "[publishScheduledNotice] notice status is not schedule, noticeId: %d, current status: %d", noticeId, noticeInfo.Status)
		return nil // 状态不匹配，可能已被手动操作，不返回错误
	}

	// 3. 更新通知状态为已发布
	updateData := map[string]interface{}{
		"status":       defines.SystemNoticeStatusPublish,
		"publish_time": time.Now().Unix(),
		"update_time":  time.Now().Unix(),
	}

	err = notice.SystemNoticeDao.UpdateById(ctx, noticeId, updateData, tx)
	if err != nil {
		tx.Rollback()
		zlog.Errorf(ctx, "[publishScheduledNotice] update notice status failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	// 4. 保存通知接收者
	receiverScope := dtonotice.ReceiverScopeParam{
		Scope:          noticeInfo.Scope,
		ScopeGroup:     noticeInfo.ScopeGroup,
		ScopeUID:       noticeInfo.ScopeUID,
		BlacklistGroup: noticeInfo.BlacklistGroup,
		BlacklistUID:   noticeInfo.BlacklistUID,
	}

	err = saveNoticeReceiver(ctx, noticeId, receiverScope, tx)
	if err != nil {
		tx.Rollback()
		zlog.Errorf(ctx, "[publishScheduledNotice] save notice receiver failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		zlog.Errorf(ctx, "[publishScheduledNotice] commit transaction failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	return nil
}

// finishScheduledNotice 处理定时结束逻辑
func finishScheduledNotice(ctx *gin.Context, noticeId int64) error {
	// 1. 检查通知是否存在
	noticeInfo, err := notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
	if err != nil {
		zlog.Errorf(ctx, "[finishScheduledNotice] get notice failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	if noticeInfo == nil {
		zlog.Warnf(ctx, "[finishScheduledNotice] notice not found, noticeId: %d", noticeId)
		return nil // 通知不存在，可能已被删除，不返回错误
	}

	// 2. 检查当前状态是否为已发布状态
	if noticeInfo.Status != defines.SystemNoticeStatusPublish {
		zlog.Warnf(ctx, "[finishScheduledNotice] notice status is not publish, noticeId: %d, current status: %d", noticeId, noticeInfo.Status)
		return nil // 状态不匹配，可能已被手动操作，不返回错误
	}

	// 3. 更新通知状态为已结束
	updateData := map[string]interface{}{
		"status":      defines.SystemNoticeStatusFinish,
		"update_time": time.Now().Unix(),
	}

	err = notice.SystemNoticeDao.UpdateById(ctx, noticeId, updateData, nil)
	if err != nil {
		zlog.Errorf(ctx, "[finishScheduledNotice] update notice status failed, noticeId: %d, err: %+v", noticeId, err)
		return err
	}

	return nil
}

// saveNoticeReceiver 保存通知接收者方法（支持事务）
func saveNoticeReceiver(ctx *gin.Context, noticeId int64, receiverScope dtonotice.ReceiverScopeParam, tx *gorm.DB) error {
	uids, err := getUidsByScope(ctx, receiverScope)
	if err != nil {
		return err
	}
	return saveNoticeReceiverByUids(ctx, noticeId, uids, tx)
}

// saveNoticeReceiverByUids 根据uids保存通知接收者（支持事务）
func saveNoticeReceiverByUids(ctx *gin.Context, noticeId int64, uids []int64, tx *gorm.DB) error {
	if len(uids) == 0 {
		return nil
	}

	var noticeUserList []notice.SystemNoticeUser
	for _, uid := range uids {
		noticeUser := notice.SystemNoticeUser{
			NoticeId:   noticeId,
			Uid:        uid,
			CreateTime: time.Now().Unix(),
			UpdateTime: time.Now().Unix(),
		}
		noticeUserList = append(noticeUserList, noticeUser)
	}

	var batchSize = 1000
	// 插入通知接收者，若之前删除过，则更新
	err := notice.SystemNoticeUserDao.BatchInsert(ctx, noticeUserList, batchSize, tx)
	if err != nil {
		return err
	}

	// 更新之前可能删除过的用户点赞、评论、评论点赞
	err = notice.SystemNoticeCommentLikeDao.UpdateDeleteByUids(ctx, noticeId, uids, batchSize, tx)
	if err != nil {
		return err
	}

	err = notice.SystemNoticeCommentDao.BatchUpdateDeleted(ctx, noticeId, uids, batchSize, tx)
	if err != nil {
		return err
	}

	err = notice.SystemNoticeUserLikeDao.BatchUpdateDeleted(ctx, noticeId, uids, batchSize, tx)
	if err != nil {
		return err
	}

	return nil
}

// getUidsByScope 根据通知范围获取用户uid列表
func getUidsByScope(ctx *gin.Context, param dtonotice.ReceiverScopeParam) ([]int64, error) {
	var uidList []int64
	var groupIds []int64
	// 筛选员工
	if param.Scope == defines.SystemNoticeScopeFilter {
		// 根据groupId进行筛选
		groupIds = utils.ParseStrToInt64s(param.ScopeGroup)
		// 根据uid进行筛选
		uidList = utils.ParseStrToInt64s(param.ScopeUID)
	}
	// 全体员工：当前只根据特定组织进行筛选
	if param.Scope == defines.SystemNoticeScopeAll {
		var config mercury.NoticeAllScope
		if err := mercury.GetConfigForJson(ctx, mercury.ConfigKeyForNoticeScope, mercury.DefaultExpireTime, &config); err != nil {
			zlog.Warnf(ctx, "[getUidsByScope] get mercury config failed, err: %+v", err)
			return nil, err
		}
		groupIds = config.GroupIds
	}

	// 根据groupId获取用户uid列表
	uids, err := getUidsByGroupIds(ctx, groupIds)
	if err != nil {
		return nil, err
	}
	uidList = append(uidList, uids...)

	// 合并结果集并去重
	uniqueList := make([]int64, 0, len(uidList))
	seen := make(map[int64]struct{}, len(uidList))
	for _, uid := range uidList {
		if _, ok := seen[uid]; !ok {
			seen[uid] = struct{}{}
			uniqueList = append(uniqueList, uid)
		}
	}

	// 应用黑名单过滤
	filteredList, err := applyBlacklistFilter(ctx, uniqueList, param)
	if err != nil {
		zlog.Warnf(ctx, "getUidsByScope: Failed to apply blacklist filter, err: %+v", err)
		// 黑名单过滤失败时，返回原始列表，不影响主流程
		return uniqueList, nil
	}

	return filteredList, nil
}

// getUidsByGroupIds 根据组织ID获取用户ID列表
func getUidsByGroupIds(ctx *gin.Context, groupIds []int64) (uids []int64, err error) {
	uids = make([]int64, 0)
	if len(groupIds) == 0 {
		return
	}

	rsp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
		GroupId:           groupIds,
		StaffType:         []int64{1}, // 只获取正式员工
		StaffStatus:       []int64{1}, // 只获取在职员工
		NeedGroupChildren: 1,
	})
	if err != nil {
		return
	}

	for _, staff := range rsp.List {
		uids = append(uids, staff.StaffUid)
	}

	return
}

// applyBlacklistFilter 应用黑名单过滤
func applyBlacklistFilter(ctx *gin.Context, uidList []int64, param dtonotice.ReceiverScopeParam) ([]int64, error) {
	if param.BlacklistGroup == "" && param.BlacklistUID == "" {
		return uidList, nil
	}

	// 获取黑名单用户ID
	blacklistUids := make(map[int64]struct{})

	// 处理黑名单组织
	if param.BlacklistGroup != "" {
		groupIds := utils.ParseStrToInt64s(param.BlacklistGroup)
		uids, err := getUidsByGroupIds(ctx, groupIds)
		if err != nil {
			return nil, err
		}
		for _, uid := range uids {
			blacklistUids[uid] = struct{}{}
		}
	}

	// 处理黑名单用户
	if param.BlacklistUID != "" {
		uids := utils.ParseStrToInt64s(param.BlacklistUID)
		for _, uid := range uids {
			blacklistUids[uid] = struct{}{}
		}
	}

	// 过滤黑名单用户
	filteredList := make([]int64, 0, len(uidList))
	for _, uid := range uidList {
		if _, isBlacklisted := blacklistUids[uid]; !isBlacklisted {
			filteredList = append(filteredList, uid)
		}
	}

	return filteredList, nil
}

// SaveNoticeReceiverWrapper 保存通知接收者的包装方法（不需要事务，供外部调用）
func SaveNoticeReceiverWrapper(ctx *gin.Context, noticeId int64, receiverScope dtonotice.ReceiverScopeParam) error {
	return saveNoticeReceiver(ctx, noticeId, receiverScope, nil)
}

// UpdateNoticeReceiverWrapper 更新通知接收者的包装方法（供外部调用）
func UpdateNoticeReceiverWrapper(ctx *gin.Context, noticeId int64, receiverScope dtonotice.ReceiverScopeParam) error {
	// 获取现有的通知接收者
	list, err := notice.SystemNoticeUserDao.List(ctx, map[string]interface{}{"notice_id": noticeId}, nil)
	if err != nil {
		return err
	}

	uids, err := getUidsByScope(ctx, receiverScope)
	if err != nil {
		return err
	}

	var addUids, delUids []int64

	mapOri := make(map[int64]struct{})
	for _, item := range list {
		mapOri[item.Uid] = struct{}{}
	}

	// 查找要新增通知的用户
	for _, item := range uids {
		if _, found := mapOri[item]; !found {
			addUids = append(addUids, item)
		}
	}

	mapNew := make(map[int64]struct{})
	for _, item := range uids {
		mapNew[item] = struct{}{}
	}

	// 查找要删除通知的用户
	for _, item := range list {
		if _, found := mapNew[item.Uid]; !found {
			delUids = append(delUids, item.Uid)
		}
	}

	err = saveNoticeReceiverByUids(ctx, noticeId, addUids, nil)
	if err != nil {
		return err
	}

	err = deleteNoticeReceiverByUids(ctx, noticeId, delUids)
	if err != nil {
		return err
	}

	return nil
}

// deleteNoticeReceiverByUids 根据uids删除通知接收者
func deleteNoticeReceiverByUids(ctx *gin.Context, noticeId int64, uids []int64) error {
	if len(uids) == 0 {
		return nil
	}

	var batchSize = 1000
	// 删除通知和用户关联
	err := notice.SystemNoticeUserDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return err
	}

	// 删除用户评论和用户点赞关联
	err = notice.SystemNoticeCommentLikeDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return err
	}

	// 删除通知和用户评论关联
	err = notice.SystemNoticeCommentDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return err
	}

	// 删除通知和用户点赞关联
	err = notice.SystemNoticeUserLikeDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return err
	}

	return nil
}
