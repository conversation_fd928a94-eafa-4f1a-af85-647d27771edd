package utils

import (
	"reflect"
	"strconv"
	"strings"

	"github.com/spf13/cast"
)

func ChunkArrayInt64(array []int64, size int) [][]int64 {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int64, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func ChunkArrayString(array []string, size int) [][]string {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]string, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func InArrayString(e string, array []string) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func InArrayInt64(e int64, array []int64) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func InArrayInt(e int, array []int) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func SliceUniq(list []string) []string {
	if len(list) == 0 {
		return list
	}

	ret := make([]string, 0, len(list))
	tmp := make(map[string]struct{})
	for _, v := range list {
		if _, ok := tmp[v]; !ok {
			ret = append(ret, v)
			tmp[v] = struct{}{}
		}
	}
	return ret
}

func IsArray(value interface{}) bool {
	rt := reflect.TypeOf(value)
	switch rt.Kind() {
	case reflect.Slice, reflect.Array:
		return true
	default:
		return false
	}
}

func ArrayUnique(arr []int64) []int64 {

	size := len(arr)
	result := make([]int64, 0, size)
	temp := map[int64]bool{}
	for i := 0; i < size; i++ {
		if _, ok := temp[arr[i]]; !ok {
			temp[arr[i]] = true
			result = append(result, arr[i])
		}
	}
	return result
}

func FindDifferenceForInt64(slice1 []int64, slice2 []int64) (s1Diff []int64, s2Diff []int64) {
	s1Diff = make([]int64, 0)
	s2Diff = make([]int64, 0)

	s1Map := make(map[int64]bool)
	for _, item := range slice1 {
		s1Map[item] = true
	}

	s2Map := make(map[int64]bool)
	for _, item := range slice2 {
		s2Map[item] = true
	}

	for _, item := range slice1 {
		if !s2Map[item] {
			s1Diff = append(s1Diff, item)
		}
	}

	for _, item := range slice2 {
		if !s1Map[item] {
			s2Diff = append(s2Diff, item)
		}
	}

	return
}

func ReverseSlice(slice []int64) []int64 {
	reversed := make([]int64, len(slice))
	for i := 0; i < len(slice); i++ {
		reversed[i] = slice[len(slice)-1-i]
	}
	return reversed
}

func ConvertArrayIntToArrayString(input []int) []string {
	ret := make([]string, 0, len(input))
	for _, e := range input {
		ret = append(ret, strconv.Itoa(e))
	}

	return ret
}

func JoinArrayIntToString(input []int, seq string) string {
	strList := ConvertArrayIntToArrayString(input)
	return strings.Join(strList, seq)
}

func ConvertArrayInt64ToArrayString(input []int64) []string {
	ret := make([]string, 0, len(input))
	for _, e := range input {
		ret = append(ret, strconv.Itoa(int(e)))
	}

	return ret
}

func JoinArrayInt64ToString(input []int64, seq string) string {
	strList := ConvertArrayInt64ToArrayString(input)
	return strings.Join(strList, seq)
}

func ConvertArrayStringToArrayInt(input []string) ([]int, error) {
	ret := make([]int, 0, len(input))
	for _, e := range input {
		intVal, err := strconv.Atoi(e)
		if err != nil {
			return nil, err
		}

		ret = append(ret, intVal)
	}

	return ret, nil
}

func ConvertArrayStringToArrayInt64(input []string) ([]int64, error) {
	ret := make([]int64, 0, len(input))
	for _, e := range input {
		intVal, err := strconv.Atoi(e)
		if err != nil {
			continue
		}

		ret = append(ret, int64(intVal))
	}

	return ret, nil
}

// 求交集
func Int64SliceIntersect(a, b []int64) []int64 {
	if 0 == len(a) || 0 == len(b) {
		return nil
	}
	aValMap := make(map[int64]bool)
	for _, v := range a {
		aValMap[v] = true
	}

	result := make([]int64, 0)
	for _, v := range b {
		if aValMap[v] {
			result = append(result, v)
		}
	}

	return result
}

func RemoveStrArrayStr(nums []string, str string) []string {
	result := make([]string, 0, len(nums))

	for _, num := range nums {
		if num == str {
			continue
		}
		result = append(result, num)
	}

	return result
}

func RemoveInt64ArrayInt64(nums []int64, item int64) []int64 {
	result := make([]int64, 0, len(nums))

	for _, num := range nums {
		if num == item {
			continue
		}
		result = append(result, num)
	}

	return result
}
func Int64ArrayToStringArray(intArray []int64) []string {
	stringArray := make([]string, len(intArray))
	for i, num := range intArray {
		stringArray[i] = strconv.FormatInt(num, 10)
	}
	return stringArray
}
func RemoveInt64ArrayInt64Array(nums []int64, item []int64) []int64 {
	result := make([]int64, 0, len(nums))

	for _, num := range nums {
		if InArrayInt64(num, item) {
			continue
		}
		result = append(result, num)
	}

	return result
}

// parseStrToInt64s 解析逗号分隔的字符串为int64切片
func ParseStrToInt64s(str string) []int64 {
	if len(strings.TrimSpace(str)) == 0 {
		return make([]int64, 0)
	}

	var result []int64
	split := strings.Split(str, ",")
	for _, s := range split {
		trimmed := strings.TrimSpace(s)
		if len(trimmed) > 0 {
			num := cast.ToInt64(trimmed)
			if num > 0 { // 只添加有效的正数ID
				result = append(result, num)
			}
		}
	}
	return result
}
